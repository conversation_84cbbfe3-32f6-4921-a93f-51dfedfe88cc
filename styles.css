/* Reset default browser margins and padding */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html, body {
  margin: 0;
  padding: 0;
  height: 100%;
  width: 100%;
}

/* Map container styles */
#map {
  height: 100vh;
  width: 100vw;
}

/* Control panel container */
#container {
  position: absolute;
  bottom: 10px;
  left: 10px;
  background-color: white;
  padding: 15px;
  border: 1px solid #ccc;
  z-index: 1000;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  transition: max-height 0.3s ease; /* Add transition for smooth minimizing */
}

/* Control panel title */
#container h3 {
  margin: 0 0 10px 0;
  color: #333;
}

/* Statistics section */
#stats {
  margin-bottom: 15px;
}

/* Statistics rows */
.stats-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 5px;
}

.stats-row:last-child {
  margin-bottom: 10px;
}

/* Visited count styling */
.visited-text {
  color: #28a745;
}

.visited-count {
  color: #28a745;
}

/* Not visited count styling */
.not-visited-text {
  color: #6c757d;
}

.not-visited-count {
  color: #6c757d;
}

/* Progress section */
.progress-section {
  background: #f8f9fa;
  padding: 8px;
  border-radius: 4px;
}

.progress-header {
  display: flex;
  justify-content: space-between;
}

.progress-bar-container {
  background: #e9ecef;
  height: 8px;
  border-radius: 4px;
  margin-top: 5px;
}

#progress-bar {
  background: #28a745;
  height: 100%;
  border-radius: 4px;
  width: 0%;
  transition: width 0.3s ease;
}

/* Button container */
.button-container {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
  margin-bottom: 15px; /* Add margin to separate from slider */
}

/* Button styles */
.btn {
  padding: 6px 12px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  gap: 5px;
}

.btn-primary {
  background: #007bff;
  color: white;
}

.btn-success {
  background: #28a745;
  color: white;
}

.btn-warning {
  background: #ffc107;
  color: #212529;
}

.btn-info {
  background: #17a2b8;
  color: white;
}

.btn-info:hover {
  background: #138496;
}

.btn:hover {
  opacity: 0.9;
}

.btn-warning:hover {
  background: #e0a800;
}

/* File input styling */
#import-file {
  display: none;
}

/* Popup content styling */
.popup-content {
  font-family: Arial, sans-serif;
}

.popup-content h3 {
  margin: 0 0 10px 0;
  color: #333;
}

.checkbox-container {
  margin-bottom: 10px;
}

.checkbox-label {
  display: flex;
  align-items: center;
  gap: 8px;
}

.checkbox-input, .checkbox-input-company {
  transform: scale(1.5);
}

.checkbox-input-company {
  accent-color: #dc3545;
}

.status-text-visited {
  color: #28a745;
}

.status-text-not-visited {
  color: #6c757d;
}

.popup-info {
  margin: 5px 0;
  color: #666;
}

.popup-info i {
  margin-right: 8px;
  width: 16px;
}

/* Company input section */
.company-input-section {
  margin-bottom: 15px;
}

.input-group {
  display: flex;
  gap: 8px;
  align-items: center;
}

.company-input {
  padding: 6px 8px;
  border: 1px solid #ccc;
  border-radius: 4px;
  font-size: 12px;
  flex: 1;
}

.company-input:focus {
  outline: none;
  border-color: #007bff;
}

/* Company info section */
.company-info {
  margin-bottom: 15px;
  padding: 8px;
  background-color: #f8f9fa;
  border-radius: 4px;
}

.company-info-content {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
}

.company-info-content i {
  color: #dc3545;
}

.company-name {
  display: flex;
  flex-direction: column;
}

.red-marker-indicator {
  background-color: #dc3545;
  color: white;
  padding: 6px 7px;
  border-radius: 3px;
  font-size: 12px;
  margin-left: auto;
}

/* Grey red markers toggle */
.grey-markers-toggle {
  margin-bottom: 15px;
  padding: 8px;
  background-color: #f8f9fa;
  border-radius: 4px;
}

.grey-markers-toggle .checkbox-label {
  font-size: 12px;
  margin: 0;
}

/* Header with minimize button */
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.header h3 {
  margin: 0;
  color: #333;
  font-size: 14px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 200px;
}

.minimize-btn {
  background: #6c757d;
  color: white;
  border: none;
  border-radius: 4px;
  width: 24px;
  height: 24px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  flex-shrink: 0;
  margin-left: 8px;
}

.minimize-btn:hover {
  background: #5a6268;
}

#tracker-content {
  transition: all 0.3s ease;
}

/* This is the existing class to hide the content */
.container-minimized #tracker-content {
  display: none;
}

.slider-container {
  margin: 10px 0;
}

.slider-container label {
  display: block;
  margin-bottom: 5px;
}

.slider-container input[type="range"] {
  width: 100%;
}

.leaflet-touch .leaflet-bar a {
	display: none;
}

/* className: 'my-div-icon' */
.my-div-icon {
  background: none;
  border: none;
  box-shadow: none;
  cursor: pointer;
  display: block;
  margin: 0;
  padding: 0;
}


/* --- MOBILE RESPONSIVENESS --- */
/* These styles will apply only on screens 768px wide or smaller */
@media (max-width: 768px) {
  #container {
    top: 10px; /* Position from the top */
    bottom: auto; /* Remove the bottom constraint */
    left: 10px;
    right: 10px;
    width: auto; /* Let left/right define the width */
    margin: 0 auto; /* Center the panel */
    max-width: 400px; /* Stop it from getting too wide on tablets */
    
    max-height: 85vh; /* Ensure it's never taller than 85% of the screen */
    overflow-y: auto; /* Make the panel itself scrollable if content overflows */
  }

  /* When minimized on mobile, remove scrolling and ensure it's small */
  #container.container-minimized {
    overflow-y: hidden; 
    max-height: 60px; /* A fixed small height for the header */
  }
}